import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:e_library/features/auth/ui/screens/login_screen.dart';
import 'package:e_library/features/auth/ui/screens/register_screen.dart';

void main() {
  group('Authentication UI Tests', () {
    testWidgets('Login screen should display all required fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LoginScreen(),
        ),
      );

      // Verify the screen title
      expect(find.text('Welcome Back!'), findsOneWidget);
      expect(find.text('Sign in to your account'), findsOneWidget);

      // Verify form fields
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email and password
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);

      // Verify buttons
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('Sign Up'), findsOneWidget);

      // Verify navigation text
      expect(find.text("Don't have an account? "), findsOneWidget);
    });

    testWidgets('Register screen should display all required fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const RegisterScreen(),
        ),
      );

      // Verify the screen title
      expect(find.text('Create Account'), findsNWidgets(2)); // Title and button
      expect(find.text('Sign up to get started'), findsOneWidget);

      // Verify form fields
      expect(find.byType(TextFormField), findsNWidgets(4)); // Name, email, password, confirm
      expect(find.text('Full Name'), findsOneWidget);
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Confirm Password'), findsOneWidget);

      // Verify buttons
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);

      // Verify navigation text
      expect(find.text("Already have an account? "), findsOneWidget);
    });

    testWidgets('Login form validation should work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LoginScreen(),
        ),
      );

      // Try to submit empty form
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Should show validation errors
      expect(find.text('Email is required'), findsOneWidget);
      expect(find.text('Password is required'), findsOneWidget);
    });

    testWidgets('Register form validation should work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const RegisterScreen(),
        ),
      );

      // Try to submit empty form
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Should show validation errors
      expect(find.text('Name is required'), findsOneWidget);
      expect(find.text('Email is required'), findsOneWidget);
      expect(find.text('Password is required'), findsOneWidget);
      expect(find.text('Please confirm your password'), findsOneWidget);
    });

    testWidgets('Password visibility toggle should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LoginScreen(),
        ),
      );

      // Find password field
      final passwordField = find.byType(TextFormField).at(1);
      
      // Find visibility toggle button
      final visibilityToggle = find.byIcon(Icons.visibility);
      expect(visibilityToggle, findsOneWidget);

      // Tap to toggle visibility
      await tester.tap(visibilityToggle);
      await tester.pump();

      // Should now show visibility_off icon
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('Email validation should work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LoginScreen(),
        ),
      );

      // Enter invalid email
      await tester.enterText(find.byType(TextFormField).first, 'invalid-email');
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Should show email validation error
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('Password length validation should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LoginScreen(),
        ),
      );

      // Enter valid email
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      
      // Enter short password
      await tester.enterText(find.byType(TextFormField).at(1), '123');
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Should show password length error
      expect(find.text('Password must be at least 6 characters'), findsOneWidget);
    });
  });
}
