# E-Library Authentication Implementation

This document describes the authentication system implemented for the E-Library Flutter application.

## Overview

The authentication system provides secure login and registration functionality with token-based authentication. It integrates with the Laravel backend API and includes proper error handling, form validation, and state management.

## Features

- ✅ **User Registration** - Create new user accounts
- ✅ **User Login** - Authenticate existing users
- ✅ **User Logout** - Secure session termination
- ✅ **Token Management** - Automatic token storage and header injection
- ✅ **Form Validation** - Client-side input validation
- ✅ **Error Handling** - Comprehensive error display
- ✅ **Loading States** - Visual feedback during API calls
- ✅ **Responsive Design** - Works on all screen sizes
- ✅ **Navigation Flow** - Seamless routing between screens

## Architecture

### Directory Structure

```
lib/features/auth/
├── data/
│   ├── models/
│   │   ├── user.dart                 # User model
│   │   ├── auth_request.dart         # Login/Register request models
│   │   └── auth_response.dart        # API response models
│   ├── api.dart                      # Authentication API calls
│   └── auth_service.dart             # Token management service
└── ui/
    ├── screens/
    │   ├── login_screen.dart         # Login screen
    │   └── register_screen.dart      # Registration screen
    └── components/
        ├── auth_form_field.dart      # Reusable form field
        └── auth_button.dart          # Reusable auth button
```

## API Integration

### Endpoints

- **Login**: `POST /auth/login`
  - Request: `{ email, password }`
  - Response: `{ success, message, token }`

- **Register**: `POST /auth/register`
  - Request: `{ name, email, password, password_confirmation }`
  - Response: `{ message, user, token }`

- **Logout**: `POST /auth/logout`
  - Response: `{ success, message }`

### Error Handling

The system handles various error scenarios:
- **401 Unauthorized**: Invalid credentials
- **422 Validation Error**: Form validation failures
- **Network Errors**: Connection issues
- **Server Errors**: Backend failures

## Key Components

### AuthService

Manages authentication tokens and state:

```dart
class AuthService {
  static Future<void> saveToken(String token);
  static Future<String?> getToken();
  static Future<void> removeToken();
  static Future<bool> isAuthenticated();
  static Future<void> initializeAuth();
}
```

### AuthApi

Handles API communication:

```dart
class AuthApi {
  static Future<LoginResponse> login(LoginRequest request);
  static Future<RegisterResponse> register(RegisterRequest request);
  static Future<LogoutResponse> logout();
}
```

### Form Validation

Client-side validation includes:
- **Email**: Valid email format required
- **Password**: Minimum 6 characters
- **Name**: Minimum 2 characters
- **Password Confirmation**: Must match password

## Usage

### Login Flow

1. User enters email and password
2. Form validation runs
3. API call to `/auth/login`
4. Token saved to SharedPreferences
5. Navigation to books screen

### Registration Flow

1. User enters name, email, password, and confirmation
2. Form validation runs
3. API call to `/auth/register`
4. Token saved to SharedPreferences
5. Navigation to books screen

### Logout Flow

1. User clicks logout button
2. API call to `/auth/logout`
3. Token removed from storage
4. Navigation to login screen

## Security Features

- **Token Storage**: Secure storage using SharedPreferences
- **Automatic Headers**: Dio interceptor adds Bearer token
- **Session Management**: Proper cleanup on logout
- **Input Validation**: Prevents malformed requests

## Testing

Run authentication tests:

```bash
flutter test test/auth_test.dart
```

Tests cover:
- Model serialization/deserialization
- Request/response handling
- Form validation logic

## Dependencies

- `shared_preferences`: Token storage
- `dio`: HTTP client
- `json_annotation`: JSON serialization
- `fl_query`: State management (for books data)

## Future Enhancements

Potential improvements:
- [ ] Biometric authentication
- [ ] Social login (Google, Apple)
- [ ] Password reset functionality
- [ ] Email verification
- [ ] Remember me option
- [ ] Session timeout handling
- [ ] Refresh token implementation

## Troubleshooting

### Common Issues

1. **Build Errors**: Run `flutter packages pub run build_runner build`
2. **Token Issues**: Clear app data or reinstall
3. **Network Errors**: Check API endpoint configuration
4. **Validation Errors**: Verify form input requirements

### Debug Mode

Enable debug logging in `AuthApi` for troubleshooting API issues.
