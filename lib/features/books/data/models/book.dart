import 'package:json_annotation/json_annotation.dart';

part 'book.g.dart';

@JsonSerializable()
class Book {
  final int id;
  final String title;
  final double price;

  @Json<PERSON>ey(name: 'publisher_id')
  final int publisherId;

  @<PERSON>son<PERSON>ey(name: 'author_id')
  final int authorId;

  const Book({
    required this.id,
    required this.title,
    required this.price,
    required this.publisherId,
    required this.authorId,
  });

  /// Creates a Book from JSON data
  factory Book.fromJson(Map<String, dynamic> json) => _$BookFromJson(json);

  /// Converts this Book to JSON
  Map<String, dynamic> toJson() => _$BookToJson(this);

  String get formattedPrice {
    return '$price\$';
  }
}
